// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters, constant_identifier_names

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader{
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String,dynamic> _ru = {
  "app": {
    "name": "Click Bazaar",
    "version": "Версия приложения"
  },
  "auth": {
    "login": {
      "title": "Вход в приложение",
      "subtitle": "Введите номер телефона\nдля входа в приложение",
      "phone_label": "Номер телефона",
      "phone_placeholder": "+998 00 000 00 00",
      "phone_validation": "Введите полный номер телефона",
      "agree_terms": "Я согласен с условиями использования",
      "terms_error": "Пожалуйста, согласитесь с условиями",
      "demo_login": "Демо вход",
      "login_button": "Отправить",
      "send_sms": "Отправить SMS",
      "login_error": "Ошибка входа",
      "login_failed": "Вход не удался. Пожалуйста, попробуйте еще раз.",
      "role_supervisor": "Контролер",
      "role_seller": "Продавец"
    },
    "sms_verification": {
      "title": "SMS подтверждение",
      "subtitle": "Введите код, отправленный\nна ваш номер телефона",
      "code_placeholder": "000000",
      "verify_button": "Отправить",
      "resend_code": "Отправить повторно",
      "resend_text": "Не получили код? ",
      "verification_error": "Ошибка подтверждения",
      "enter_code_title": "Введите код",
      "enter_code_subtitle": " отправленный на ваш номер\n6-значный SMS код",
      "code_validation_error": "Введите 6-значный SMS код",
      "sms_resent_success": "SMS код отправлен повторно",
      "generic_error": "Произошла ошибка",
      "verification_failed": "SMS код не подтвержден",
      "unexpected_error": "Неожиданная ошибка: ",
      "sending": "Отправляется...",
      "resend_with_countdown": "Отправить повторно ({countdown})",
      "resend_simple": "Отправить повторно"
    },
    "role_selection": {
      "title": "Выберите роль",
      "subtitle": "В какой роли вы хотите\nиспользовать приложение?",
      "continue_button": "Продолжить",
      "nazoratchi": "Контролер",
      "nazoratchi_description": "Контролирующий сотрудник",
      "sotuvchi": "Продавец",
      "sotuvchi_description": "Занимающийся торговлей"
    },
    "services": {
      "sms_sent": "SMS код отправлен",
      "error_occurred": "Произошла ошибка: ",
      "login_success": "Успешный вход",
      "invalid_sms_code": "Неверный SMS код",
      "demo_login": "Вход в демо режиме",
      "no_internet": "Нет интернет соединения. Пожалуйста, проверьте подключение.",
      "sms_resent": "SMS код отправлен повторно",
      "sms_send_error": "Ошибка отправки SMS кода",
      "server_error": "Ошибка сервера",
      "general_error": "Произошла ошибка",
      "connection_timeout": "Время соединения истекло",
      "receive_timeout": "Время ожидания ответа истекло",
      "connection_error": "Нет интернет соединения",
      "bad_request": "Неверные данные",
      "unexpected_error": "Произошла неожиданная ошибка"
    }
  },
  "navigation": {
    "home": "Главная",
    "statistics": "Статистика",
    "market_structure": "Структура рынка",
    "payment_history": "История платежей",
    "empty_places": "Свободные места",
    "profile": "Профиль"
  },
  "market_structure": {
    "title": "Структура рынка",
    "error_occurred": "Произошла ошибка",
    "no_pavilions": "Павильоны не найдены",
    "no_blocks": "Блоки недоступны",
    "no_blocks_subtitle": "В настоящее время в этом павильоне нет блоков"
  },
  "profile": {
    "men": "Мужской",
    "women": "Женский",
    "personal_info": "Личная информация",
    "personal_info_subtitle": "Данные профиля, должностная информация",
    "seller_personal_info_subtitle": "Данные профиля, прикрепленные места",
    "biometric_data": "Биометрические данные",
    "biometric_data_subtitle": "Просмотр подтвержденного лица",
    "notifications": "Уведомления",
    "notifications_subtitle": "Настройки push уведомлений",
    "language_selection": "Выбор языка",
    "language_subtitle_uzbek": "Узбекский (по умолчанию)",
    "language_subtitle_russian": "Русский",
    "logout": "Выход",
    "logout_subtitle": "Выйти из аккаунта",
    "edit_profile": "Редактировать профиль",
    "save_changes": "Сохранить изменения",
    "cancel": "Отмена",
    "select_image": "Выбрать изображение",
    "camera": "Камера",
    "gallery": "Галерея",
    "first_name": "Имя",
    "last_name": "Фамилия",
    "middle_name": "Отчество",
    "phone_number": "Номер телефона",
    "gender": "Пол",
    "address": "Адрес",
    "pavilion": "Павильон",
    "blocks": "Блоки",
    "places": "Места",
    "blocks_count": " блоков",
    "places_count": " мест",
    "default_user_name": "Пользователь",
    "loading_info": "Загрузка информации...",
    "user_data_not_found": "Данные пользователя не найдены",
    "authentication_error": "Ошибка аутентификации",
    "image_selection_error": "Ошибка при выборе изображения",
    "logout_confirmation": "Вы действительно хотите выйти из системы?",
    "logout_error": "Ошибка при выходе",
    "profile_loaded_from_cache": "Профиль загружен из кэша",
    "failed_to_load_profile": "Ошибка загрузки профиля",
    "profile_refreshed_successfully": "Профиль успешно обновлен",
    "failed_to_refresh_profile": "Ошибка обновления профиля",
    "failed_to_load_cached_profile": "Ошибка загрузки кэшированного профиля",
    "profile_image_updated_successfully": "Изображение профиля успешно обновлено",
    "profile_image_updated": "Изображение профиля обновлено",
    "profile_image_update_error": "Ошибка при обновлении изображения профиля",
    "old_profile_image_restored": "Старое изображение профиля восстановлено",
    "profile_image_update_and_reload_error": "Ошибка при обновлении изображения профиля и перезагрузке",
    "old_profile_image_restored_from_cache": "Старое изображение профиля восстановлено из кэша"
  },
  "language": {
    "dialog_title": "Выберите язык",
    "uzbek": "O'zbek",
    "russian": "Русский",
    "english": "English",
    "save": "Сохранить",
    "cancel": "Отмена"
  },
  "home": {
    "tariff": "Тариф"
  },
  "payment": {
    "daily_rate": "Дневная ставка",
    "daily_tariff": "Дневной тариф",
    "category": "Категория",
    "tariff": "Тариф",
    "place_size": "Размер места",
    "total_square": "Общая площадь",
    "debt": "Задолженность",
    "payment_success": "Платеж успешно\nпринят!",
    "payment_error": "Ошибка платежа",
    "cash_payment_title": "за {amount}\nприняли?",
    "accept_payment": "Принял",
    "payment_creation_error": "Ошибка создания платежа",
    "payment_confirmation_error": "Ошибка подтверждения платежа",
    "payment_process_error": "Ошибка процесса платежа"
  },
  "places": {
    "empty_place": "Пустое место",
    "unusable_place": "Непригодное место",
    "needs_repair": "Требует ремонта",
    "dirty_place": "Грязное место",
    "broken_equipment": "Сломанное оборудование",
    "place_number": "Место",
    "block": "блок",
    "pavilion": "Свежие фрукты",
    "fresh_fruits": "Свежие фрукты",
    "no_empty_places": "Свободные места не найдены",
    "no_empty_places_subtitle": "В настоящее время нет свободных мест для аренды",
    "category": "Категория:",
    "rental_price": "Цена аренды:",
    "contact": "Связаться",
    "contact_message": "Для получения подробной информации об этом месте свяжитесь с администрацией рынка.",
    "place_numbers": "Номера мест",
    "not_found": "Места не найдены",
    "not_found_subtitle": "В настоящее время в этом блоке нет мест",
    "call": "Позвонить",
    "rental_price": "Аренда: {price}"
  },
  "common": {
    "save": "Сохранить",
    "cancel": "Отмена",
    "close": "Закрыть",
    "ok": "OK",
    "yes": "Да",
    "no": "Нет",
    "loading": "Загрузка...",
    "error": "Ошибка",
    "error_occurred": "Произошла ошибка",
    "success": "Успех",
    "retry": "Повторить",
    "refresh": "Обновить",
    "search": "Поиск",
    "filter": "Фильтр",
    "clear": "Очистить",
    "no_internet_connection": "Нет интернет-соединения. Пожалуйста, проверьте подключение.",
    "unknown_seller": "Неизвестный продавец"
  },
  "date_picker": {
    "dialog_title": "Выберите дату",
    "ok": "OK",
    "cancel": "Отмена",
    "months": {
      "january": "Январь",
      "february": "Февраль",
      "march": "Март",
      "april": "Апрель",
      "may": "Май",
      "june": "Июнь",
      "july": "Июль",
      "august": "Август",
      "september": "Сентябрь",
      "october": "Октябрь",
      "november": "Ноябрь",
      "december": "Декабрь"
    },
    "weekdays": {
      "monday": "Понедельник",
      "tuesday": "Вторник",
      "wednesday": "Среда",
      "thursday": "Четверг",
      "friday": "Пятница",
      "saturday": "Суббота",
      "sunday": "Воскресенье"
    },
    "weekdays_short": {
      "monday": "Пн",
      "tuesday": "Вт",
      "wednesday": "Ср",
      "thursday": "Чт",
      "friday": "Пт",
      "saturday": "Сб",
      "sunday": "Вс"
    }
  },
  "errors": {
    "network_error": "Ошибка сетевого соединения",
    "server_error": "Ошибка сервера",
    "unknown_error": "Неизвестная ошибка",
    "timeout_error": "Время истекло",
    "unauthorized": "Не авторизован",
    "forbidden": "Запрещено",
    "not_found": "Не найдено",
    "bad_request": "Неверный запрос",
    "internal_server_error": "Внутренняя ошибка сервера",
    "no_internet": "Нет интернет соединения",
    "payment_process_error": "Ошибка процесса платежа:",
    "no_data_available": "Данные недоступны",
    "connection_timeout": "Время соединения истекло. Пожалуйста, попробуйте снова.",
    "send_timeout": "Время отправки данных истекло. Пожалуйста, попробуйте снова.",
    "receive_timeout": "Время получения данных истекло. Пожалуйста, попробуйте снова.",
    "request_cancelled": "Запрос отменен.",
    "connection_error": "Ошибка соединения. Проверьте интернет соединение.",
    "bad_request_detailed": "Неверный запрос. Проверьте данные.",
    "unauthorized_detailed": "Ошибка авторизации. Войдите снова.",
    "forbidden_detailed": "Доступ запрещен. У вас нет прав для выполнения этого действия.",
    "not_found_detailed": "Данные не найдены.",
    "conflict": "Конфликт данных.",
    "validation_error": "Неверный формат данных.",
    "too_many_requests": "Слишком много запросов. Подождите немного.",
    "internal_server_detailed": "Ошибка сервера. Пожалуйста, попробуйте позже.",
    "bad_gateway": "Сервер не отвечает.",
    "service_unavailable": "Сервис временно недоступен.",
    "server_error_with_code": "Ошибка сервера ({code}).",
    "generic_error": "Неожиданная ошибка: {error}",
    "network_check_failed": "Нет интернет-соединения. Пожалуйста, проверьте подключение.",
    "network_connection_error": "Нет интернет соединения. Пожалуйста, проверьте подключение.",
    "demo_login_error": "Ошибка демо входа"
  },
  "image_errors": {
    "unsupported_format": "Формат изображения не поддерживается",
    "network_error": "Нет интернет соединения",
    "corrupted_image": "Изображение повреждено",
    "image_not_found": "Изображение не найдено",
    "loading_error": "Ошибка загрузки изображения"
  },
  "dialogs": {
    "empty_square": {
      "status_empty": "Пустой",
      "description": "Это место сейчас пустое. Торговая деятельность не ведется. Открыто для аренды",
      "mark_as_empty": "Отметить как пустое место",
      "place_number": "Место #{number}"
    },
    "contact": {
      "call_button": "Позвонить"
    },
    "loading": {
      "default_message": "Загрузка...",
      "cancel": "Отмена",
      "hiding_dialog": "LoadingDialog: Скрытие диалога загрузки",
      "cannot_pop": "LoadingDialog: Невозможно закрыть - нет диалога для закрытия"
    },
    "cash_payment": {
      "cancel": "Отмена",
      "confirm_error": "Ошибка подтверждения платежа",
      "process_error": "Ошибка обработки платежа: {error}",
      "amount_format_comment": "Извлечь число из строки суммы типа \"1 kunlik (12 000 UZS)\""
    },
    "payment_status": {
      "creating": "Создание платежа...",
      "existing_payment_found": "Найден существующий платеж",
      "payment_created": "Платеж создан",
      "confirming": "Подтверждение платежа...",
      "paid": "Оплачено!",
      "error_occurred": "Произошла ошибка",
      "pending_payment_notice": "Этот платеж был создан ранее и еще не подтвержден",
      "unknown_error": "Неизвестная ошибка",
      "delete_payment": "Удалить",
      "accept_payment": "Принял",
      "show_qr_code": "Показать QR код",
      "continue": "Продолжить",
      "close": "Закрыть",
      "retry": "Повторить",
      "cheque_number": "Номер чека:",
      "created_at": "Создан:",
      "days_count": "Количество дней:",
      "status": "Статус:",
      "status_pending": "Ожидание",
      "status_new": "Новый",
      "days_unit": "дней",
      "places_label": "Места:",
      "delete_error": "Ошибка удаления чека",
      "delete_error_with_message": "Ошибка удаления чека: {error}",
      "creation_error": "Ошибка создания платежа",
      "creation_error_with_message": "Ошибка создания платежа: {error}",
      "confirm_error": "Ошибка подтверждения платежа",
      "process_error": "Ошибка обработки платежа: {error}",
      "unconfirmed_payment_exists": "У вас есть неподтвержденный платеж.\n\nСначала подтвердите или отмените существующий платеж, затем создайте новый.",
      "success_status": "success",
      "qr_show_button": "Показать QR код",
      "grid_refresh_success": "Обновление сетки после успешного платежа с blockId: {blockId}, pavilionId: {pavilionId}",
      "grid_refresh_error": "Ошибка обновления сетки: {error}",
      "date_format_comment": "Использовать поле даты из ответа API (формат: \"2025-07-16 16:34\")",
      "time_empty": "",
      "date_contains_space": " ",
      "date_parts_separator": " ",
      "date_part_index_0": "2025-07-16",
      "date_part_index_1": "16:34",
      "date_format_conversion": "Форматировать дату из \"2025-07-16\" в \"16.07.2025\"",
      "date_separator": "-",
      "formatted_date_pattern": "{day}.{month}.{year}",
      "formatted_datetime": "{date} {time}",
      "cheque_id_display": "#{id}",
      "days_display": "{count} дней"
    },
    "place_check": {
      "checking": "Проверка...",
      "please_wait": "Пожалуйста, подождите",
      "success_message": "Успешно проверено!",
      "error_occurred": "Произошла ошибка",
      "unknown_error": "Неизвестная ошибка",
      "max_retries_reached": "Достигнуто максимальное количество попыток. Пожалуйста, попробуйте позже.",
      "attempt_count": "Попытка: {current}/{max}",
      "cancel": "Отмена",
      "close": "Закрыть",
      "retry": "Повторить",
      "check_error": "Произошла ошибка при проверке",
      "unexpected_error": "Неожиданная ошибка: {error}"
    },
    "qr_payment": {
      "title": "Оплата через Click",
      "place_number": "Место #{number}",
      "generating_qr": "Создание QR кода...",
      "scan_instruction": "Отсканируйте QR-код\nдля совершения платежа",
      "check_payment": "Проверить платеж",
      "retry": "Повторить",
      "creation_error": "Ошибка создания платежа",
      "creation_error_with_message": "Ошибка создания платежа: {error}",
      "qr_generation_error": "Ошибка создания QR кода: {error}",
      "payment_not_found": "Платеж не найден",
      "check_error": "Ошибка проверки платежа: {error}",
      "brightness_error": "Ошибка установки яркости: {error}",
      "brightness_restore_error": "Ошибка восстановления яркости: {error}",
      "error_message_empty": "",
      "service_id_comment": "Замените на ваш реальный ID сервиса",
      "merchant_id_comment": "Замените на ваш реальный ID мерчанта",
      "merchant_user_id_comment": "Замените на ваш реальный ID пользователя мерчанта"
    },
    "terminal_payment": {
      "paid_button": "Оплачено",
      "cancel": "Отмена",
      "confirm_error": "Ошибка подтверждения платежа",
      "process_error": "Ошибка обработки платежа: {error}",
      "amount_format_comment": "Извлечь число из строки суммы типа \"1 kunlik (12 000 UZS)\""
    },
    "square_dialog": {
      "payment_days_question": "На сколько дней хотите оплатить?",
      "payment_button": "Оплатить",
      "verified_button": "Проверено",
      "mark_empty_button": "Отметить как пустое место",
      "select_payment_method": "Выберите способ оплаты",
      "pay_with_click": "Оплатить через Click",
      "pay_with_cash": "Оплатить наличными",
      "pay_with_terminal": "Оплатить через терминал",
      "seller_placeholder": "-",
      "currency_uzs": "UZS",
      "square_meter": "м²",
      "debug_no_seller_payment": "Невозможно продолжить платеж: Нет действительного продавца",
      "debug_no_seller_cash": "Невозможно продолжить наличный платеж: Нет действительного продавца",
      "debug_no_seller_terminal": "Невозможно продолжить терминальный платеж: Нет действительного продавца",
      "demo_place_id": "demo_place_id",
      "debug_checking_conditions": "🔍 Проверка условий кнопки Tekshirildi:",
      "debug_api_mode": "   - API режим: allUnbinded={allUnbinded}, allHaveNoSeller={allHaveNoSeller}",
      "debug_squares_info": "   - Квадраты: {squares}",
      "debug_seller_details": "   - Детали продавца: {sellers}",
      "debug_final_result": "   - Финальный результат: {result}",
      "debug_legacy_mode": "   - Устаревший режим: isUnbinded={isUnbinded}, hasNoSeller={hasNoSeller}",
      "debug_square_info": "   - Квадрат: status={status}, seller={seller}",
      "debug_seller_found_grouped": "Найден ID продавца из сгруппированных квадратов: {sellerId}",
      "debug_seller_null_grouped": "Продавец null или пустой в сгруппированных квадратах",
      "debug_seller_found_single": "Найден ID продавца из одного квадрата: {sellerId}",
      "debug_no_seller_found": "Продавец не найден ни в сгруппированных квадратах, ни в одном квадрате",
      "debug_demo_seller_warning": "Предупреждение: Продавец не найден в данных места, используется демо ID продавца",
      "demo_user_id": "demo_user_id",
      "debt_amount": "{amount} UZS",
      "total_amount": "{amount} UZS"
    }
  },
  "nazoratchi": {
    "reports": {
      "empty_square_report": {
        "title": "Отметить как пустое место",
        "image_upload": "Загрузка изображения",
        "image_upload_subtitle": "Загрузите фото пустого места",
        "description": "Написать комментарий",
        "submit": "Отправить",
        "hint_text": "Это место сейчас пустое. Торговая деятельность не ведется. Открыто для аренды",
        "quick_tags": {
          "empty_place": "Пустое место",
          "unusable_place": "Непригодное место",
          "needs_repair": "Требует ремонта",
          "dirty_place": "Грязное место",
          "broken_equipment": "Сломанное оборудование"
        },
        "image_picker": {
          "select_image": "Выбрать изображение",
          "camera": "Камера",
          "gallery": "Галерея",
          "error_selecting_image": "Ошибка при выборе изображения",
          "camera_preview_message": "Пожалуйста, проверьте полученное изображение и при необходимости исправьте ориентацию"
        },
        "success_dialog": {
          "title": "Отчет успешно отправлен",
          "message": "Ваш отчет был успешно отправлен и будет рассмотрен",
          "ok_button": "Хорошо"
        },
        "square_selection": {
          "title": "Какое место вы хотите освободить"
        }
      }
    },
    "statistics": {
      "all": "Общий",
      "plan_in_plan": "По плану",
      "plan_received": "Поступило",
      "plan_via_click": "Через Click",
      "plan_cash": "Наличные",
      "plan_via_terminal": "Через терминал",
      "plan_debt": "Задолженность",
      "legend_paid_places": "Оплаченные места",
      "legend_unpaid_places": "Неоплаченные места",
      "legend_unassigned_places": "Неназначенные места",
      "legend_empty_places": "Пустые места",
      "payment_delivered": "Доставлено",
      "payment_not_delivered": "Не доставлено"
    },
    "payment": {
      "terminal_payment_title": "{amount} за {formattedAmount}\nоплачено через терминал?",
      "cash_payment_title": "{amount} за {formattedAmount}\nприняли?",
      "payment_rejected": "Платеж не принят!",
      "ok_button": "Хорошо"
    },
    "tuzilma": {
      "no_data_available": "Данные недоступны",
      "error_occurred": "Произошла ошибка",
      "retry": "Повторить",
      "payment_impossible": "Платеж невозможен",
      "no_seller_info": "Информация о продавце для этого места недоступна. Для совершения платежа сначала должен быть назначен продавец.",
      "default_category": "Свежие фрукты",
      "place_number": "Место #{number}",
      "default_seller_name": "Азаматов Ахрор",
      "default_empty_description": "Это место в настоящее время пустое. Торговая деятельность не ведется. Открыто для аренды",
      "debt_loading_error": "Ошибка при загрузке данных",
      "status": {
        "paid": "Оплачено",
        "unpaid": "Не оплачено",
        "unassigned": "Не назначено",
        "empty": "Пустой",
        "unknown": "Неизвестно"
      },
      "errors": {
        "network_connection": "Нет интернет соединения. Пожалуйста, проверьте подключение.",
        "network_unavailable": "Нет интернет соединения",
        "pavilion_load_error": "Ошибка при загрузке данных павильона",
        "blocks_load_error": "Ошибка при загрузке данных блоков",
        "unexpected_error": "Неожиданная ошибка",
        "status_update_failed": "Статус не обновлен",
        "report_not_sent": "Отчет не отправлен",
        "image_file_not_found": "Файл изображения не найден"
      },
      "success": {
        "empty_place_marked": "Пустое место успешно отмечено",
        "empty_place_mark_error": "Ошибка при отметке пустого места"
      },
      "dialog": {
        "unexpected_error": "Произошла неожиданная ошибка",
        "place_number": "Место #{number}",
        "total_amount": "Общая сумма:",
        "total_square": "Общая площадь:",
        "payment_days_question": "За сколько дней хотите заплатить?",
        "category": "Категория:",
        "tariff": "Тариф:",
        "daily_rate": "1 день ({amount} UZS)",
        "place_size": "Размер места:",
        "place_size_meters": "{size} метр",
        "debt": "Задолженность",
        "last_payment_date": "Дата последнего платежа:",
        "last_payment_amount": "Сумма последнего платежа:",
        "debt_days": "{days} дней",
        "seller_placeholder": "-",
        "currency_uzs": "UZS",
        "square_meter": "м²",
        "error_title": "Ошибка",
        "data_not_loaded": "Данные не загружены",
        "make_payment": "Совершить платеж",
        "checked": "Проверено",
        "mark_empty_place": "Отметить как пустое место",
        "select_payment_method": "Выберите способ оплаты",
        "pay_with_click": "Оплатить через Click",
        "pay_with_cash": "Оплатить наличными",
        "pay_with_terminal": "Оплатить через терминал"
      }
    },
    "market_structure": {
      "blocks": "Блоки",
      "places": "Места",
      "total_places": "Всего мест",
      "category": "Категория:",
      "tariff": "Тариф:",
      "place_size": "Размер места:",
      "total_square": "Общая площадь:",
      "daily_rate": "Дневная ставка",
      "meter": "метр",
      "debt": "Задолженность",
      "total_amount": "Общая сумма:",
      "legend": {
        "paid": "Оплачено",
        "unpaid": "Не оплачено",
        "empty": "Пустой",
        "unassigned": "Не назначено"
      },
      "detailed": "Более"
    }
  },
  "sotuvchi": {
    "home": {
      "title": "Главная",
      "tariff": "Тариф"
    },
    "payment_history": {
      "no_payments_title": "История платежей недоступна",
      "no_payments_subtitle": "Пока не было совершено ни одного платежа",
      "place_number": "Номер места:",
      "tariff": "Тариф:",
      "paid_date": "Оплачено:"
    },
    "profile": {
      "title": "Профиль",
      "logout_dialog_title": "Выход",
      "logout_dialog_content": "Вы действительно хотите выйти из системы?",
      "logout_dialog_cancel": "Отмена",
      "logout_dialog_confirm": "Выход",
      "image_selection_error": "Ошибка при выборе изображения",
      "logout_error": "Ошибка при выходе",
      "load_error": "Ошибка при загрузке данных профиля",
      "image_update_error": "Ошибка при обновлении изображения профиля",
      "cache_loaded": "Профиль загружен из кеша",
      "refresh_success": "Профиль успешно обновлен",
      "load_failed": "Не удалось загрузить профиль",
      "refresh_failed": "Не удалось обновить профиль"
    },
    "empty_places": {
      "load_error": "Ошибка при загрузке данных о свободных местах",
      "network_error": "Нет интернет соединения. Пожалуйста, проверьте подключение.",
      "data_load_error": "Ошибка при загрузке данных"
    },
    "settings": {
      "notifications": "Уведомления",
      "notifications_subtitle": "Получать уведомления о новых сообщениях",
      "theme": "Тема",
      "theme_subtitle": "Светлая или темная тема",
      "auto_refresh": "Автообновление",
      "auto_refresh_subtitle": "Автоматически обновлять данные",
      "theme_selection": "Тема",
      "theme_selection_subtitle": "Светлая или темная тема"
    }
  },
  "face_control": {
    "title": "Система контроля лица",
    "enter_face": "Ввести лицо",
    "confirm_face": "Подтвердить лицо",
    "settings": "Настройки",
    "about": "О программе",
    "refresh": "Обновить",
    "please_refresh_page": "Пожалуйста, обновите страницу",
    "warning_message": "Внимание! Для хорошего результата следуйте следующим рекомендациям:",
    "warning_details": "• Находиться в хорошо освещенном месте\n• На изображении должно быть только одно лицо\n• Убрать очки, маски и все аксессуары, закрывающие лицо",
    "camera_preview_message": "Пожалуйста, проверьте полученное изображение, при необходимости исправьте ориентацию. Ваше лицо должно быть прямо!",
    "errors": {
      "check_internet": "Проверьте интернет-соединение",
      "user_not_found": "Информация о пользователе не найдена",
      "unknown_role": "Неизвестная роль пользователя: ",
      "invalid_user_data": "Неверные данные пользователя",
      "profile_load_error": "Ошибка загрузки профиля: ",
      "download_error": "Не удалось загрузить данные, FACE_IMAGE неверен!",
      "image_capture_error": "Ошибка захвата изображения: ",
      "image_process_error": "Ошибка обработки изображения: ",
      "logout_error": "Ошибка при выходе: "
    },
    "settings_page": {
      "title": "Настройки (Для отладки)",
      "camera_lens": "Объектив камеры",
      "front": "Передний",
      "thresholds": "Пороги",
      "liveness_level": "Уровень живости",
      "liveness_threshold": "Порог живости",
      "identify_threshold": "Порог идентификации",
      "reset": "Сброс",
      "restore_default": "Восстановить настройки по умолчанию",
      "clear_all_person": "Очистить всех людей",
      "cancel": "Отмена",
      "ok": "OK"
    },
    "about_page": {
      "title": "Premium Soft",
      "developer_message": "Привет! Я разработчик. Если у вас проблемы с контролем лица, свяжитесь со мной!",
      "email": "Email: <EMAIL>",
      "phone": "Тел: +998911283725",
      "telegram": "Telegram: @flutterblogs",
      "github": "Github: https://github.com/mamasodikov"
    },
    "functional_page": {
      "unlock_app": "Разблокировать приложение",
      "support_info": "Информация поддержки",
      "time_check": "Сравнение времени устройства с сервером",
      "location_check": "Проверка подделки местоположения",
      "mock_location_check": "Проверка подделки местоположения",
      "waiting": "Ожидание...",
      "please_fix_issues": "Пожалуйста, устраните вышеуказанные проблемы...",
      "time_difference": "Разница с серверным временем: {difference} минут"
    },
    "person_view": {
      "approved": "Подтверждено",
      "not_approved": "Не подтверждено",
      "admin_only_delete": "Изображение может удалить только администратор.."
    },
    "face_detection": {
      "face_control": "Контроль лица",
      "error_uploading": "Ошибка при загрузке",
      "admin_not_approved": "Администратор не подтвердил",
      "uploaded": "Загружено",
      "approved": "Подтверждено",
      "photo_uploaded": "Фото загружено",
      "similarity": "Сходство: {percent}%",
      "liveness": "Живость: {percent}%",
      "liveness_calculating": "Сходство..",
      "upload_error_message": "Ошибка: {message}"
    }
  },
  "camera": {
    "title": "Съемка фото",
    "selected_image": "Выбранное изображение",
    "captured_image": "Снятое изображение",
    "camera_preparing": "Подготовка камеры...",
    "gallery": "Галерея",
    "settings": "Настройки",
    "camera_settings": "Настройки камеры",
    "hd_quality": "Высокое качество (HD)",
    "hd_quality_subtitle": "Улучшает качество изображения",
    "grid_lines": "Направляющие линии",
    "grid_lines_subtitle": "Для композиции изображения",
    "auto_save": "Автоматическое сохранение",
    "auto_save_subtitle": "Автоматически сохранять изображения в галерею",
    "image_format": "Формат изображения",
    "image_format_current": "Текущий: {format}",
    "select_image_format": "Выберите формат изображения",
    "jpeg_description": "Малый размер файла, хорошее качество",
    "png_description": "Большой размер файла, лучшее качество",
    "cancel": "Отмена",
    "image_saved_to_gallery": "Изображение сохранено в галерею",
    "save_error": "Ошибка при сохранении: {error}",
    "gallery_selection_error": "Ошибка при выборе из галереи: {error}",
    "camera_error": "Ошибка камеры: {error}",
    "rotation_error": "Повернутый результат не существует!"
  },
  "location": {
    "turn_on_location": "Включить местоположение",
    "give_location_permission": "Разрешить местоположение",
    "fraud_location": "Обнаружено мошенничество с местоположением",
    "turn_on_location_message": "Пожалуйста, включите местоположение, чтобы приложение работало правильно!",
    "give_location_permission_message": "Разрешить местоположение",
    "fraud_location_message": "Обнаружено мошенничество с местоположением!"
  },
  "statistics_history": {
    "error_occurred": "Произошла ошибка",
    "load_more_error": "Ошибка при загрузке дополнительных данных",
    "data_load_error": "Ошибка при загрузке данных",
    "retry": "Повторить",
    "no_data_found": "Данные не найдены",
    "places_header": "Места",
    "amount_header": "Сумма (UZS)",
    "payment_history_load_error": "Ошибка при загрузке истории платежей",
    "statistics_load_error": "Ошибка при загрузке статистики",
    "invalid_response_format": "Неверный формат ответа"
  },
  "camera": {
    "settings_title": "Настройки камеры",
    "hd_quality": "Высокое качество (HD)",
    "hd_quality_subtitle": "Улучшает качество изображения",
    "grid_lines": "Направляющие линии",
    "grid_lines_subtitle": "Для композиции изображения",
    "auto_save": "Автосохранение",
    "auto_save_subtitle": "Автоматически сохранять изображения в галерею",
    "image_format": "Формат изображения",
    "image_format_current": "Текущий: {format}",
    "select_image_format": "Выберите формат изображения",
    "jpeg_description": "Малый размер файла, хорошее качество",
    "png_description": "Большой размер файла, лучшее качество",
    "cancel": "Отмена",
    "gallery_selection_error": "Ошибка выбора изображения из галереи: {error}",
    "image_saved_to_gallery": "Изображение сохранено в галерею",
    "save_error": "Ошибка сохранения: {error}",
    "capture_error": "Ошибка захвата изображения: {error}"
  },
  "face_control": {
    "errors": {
      "user_not_found": "Пользователь не найден",
      "unknown_role": "Неизвестная роль: ",
      "invalid_user_data": "Неверные данные пользователя",
      "profile_load_error": "Ошибка загрузки профиля: ",
      "download_error": "Ошибка загрузки",
      "image_capture_error": "Ошибка захвата изображения: ",
      "image_process_error": "Ошибка обработки изображения: ",
      "logout_error": "Ошибка выхода: ",
      "face_not_detected": "Лицо не обнаружено"
    }
  },
  "payment": {
    "errors": {
      "bad_request": "Отправлены неверные данные",
      "unauthorized": "Ошибка авторизации",
      "not_found": "Платеж не найден",
      "conflict": "Платеж уже существует",
      "server_error": "Ошибка сервера",
      "payment_error": "Ошибка в процессе платежа",
      "unexpected_error": "Произошла неожиданная ошибка"
    },
    "types": {
      "cash": "Оплата наличными",
      "terminal": "Оплата через терминал",
      "qr": "Оплата через Click",
      "cash_short": "Наличные",
      "terminal_short": "Терминал",
      "qr_short": "Click"
    }
  },
  "market_structure": {
    "no_pavilions_subtitle": "Пока нет доступных павильонов"
  }
};
static const Map<String,dynamic> _en = {
  "app": {
    "name": "Click Bazaar",
    "version": "App Version"
  },
  "nazoratchi": {
    "market_structure": {
      "total_places": "Total Places"
    }
  }
};
static const Map<String,dynamic> _uz = {
  "app": {
    "name": "Click Bazaar",
    "version": "Ilova versiyasi"
  },
  "auth": {
    "login": {
      "title": "Ilovaga kirish",
      "subtitle": "Ilovaga kirish uchun telefon\nraqamingizni kiriting",
      "phone_label": "Telefon raqam",
      "phone_placeholder": "+998 00 000 00 00",
      "phone_validation": "To'liq telefon raqamini kiriting",
      "agree_terms": "Men foydalanish shartlariga rozlik bildiraman",
      "terms_error": "Iltimos, shartlarga rozilik bering",
      "demo_login": "Demo kirish",
      "login_button": "Yuborish",
      "send_sms": "SMS yuborish",
      "login_error": "Kirish xatoligi",
      "login_failed": "Kirish muvaffaqiyatsiz. Iltimos, qayta urinib ko'ring.",
      "role_supervisor": "Nazoratchi",
      "role_seller": "Sotuvchi"
    },
    "sms_verification": {
      "title": "SMS tasdiqlash",
      "subtitle": "Telefon raqamingizga yuborilgan\nkodni kiriting",
      "code_placeholder": "000000",
      "verify_button": "Yuborish",
      "resend_code": "Qayta yuborish",
      "resend_text": "Kodni olmadingizmi? ",
      "verification_error": "Tasdiqlash xatoligi",
      "enter_code_title": "Kodni kiriting",
      "enter_code_subtitle": " raqamingizga\nyuborilgan 6 xonali SMS kodni kiriting",
      "code_validation_error": "6 xonali SMS kodni kiriting",
      "sms_resent_success": "SMS kod qayta yuborildi",
      "generic_error": "Xatolik yuz berdi",
      "verification_failed": "SMS kod tasdiqlanmadi",
      "unexpected_error": "Kutilmagan xatolik: ",
      "sending": "Yuborilmoqda...",
      "resend_with_countdown": "Qayta yuborish ({countdown})",
      "resend_simple": "Qayta yuborish"
    },
    "role_selection": {
      "title": "Rolni tanlang",
      "subtitle": "Qaysi rol sifatida\nilovadan foydalanmoqchisiz?",
      "continue_button": "Davom etish",
      "nazoratchi": "Nazoratchi",
      "nazoratchi_description": "Nazorat qiluvchi xodim",
      "sotuvchi": "Sotuvchi",
      "sotuvchi_description": "Savdo-sotiq bilan shug'ullanuvchi"
    },
    "services": {
      "sms_sent": "SMS kod yuborildi",
      "error_occurred": "Xatolik yuz berdi: ",
      "login_success": "Muvaffaqiyatli kirish",
      "invalid_sms_code": "Noto'g'ri SMS kod",
      "demo_login": "Demo rejimda kirish",
      "no_internet": "Internet aloqasi yo'q. Iltimos, ulanishni tekshiring.",
      "sms_resent": "SMS kod qayta yuborildi",
      "sms_send_error": "SMS kod yuborishda xatolik",
      "server_error": "Server xatoligi",
      "general_error": "Xatolik yuz berdi",
      "connection_timeout": "Ulanish vaqti tugadi",
      "receive_timeout": "Javob kutish vaqti tugadi",
      "connection_error": "Internet aloqasi yo'q",
      "bad_request": "Noto'g'ri ma'lumot",
      "unexpected_error": "Kutilmagan xatolik yuz berdi"
    }
  },
  "navigation": {
    "home": "Asosiy",
    "statistics": "Statistika",
    "market_structure": "Bozor tuzilmasi",
    "payment_history": "To'lov tarixi",
    "empty_places": "Bo'sh joylar",
    "profile": "Profil"
  },
  "market_structure": {
    "title": "Bozor tuzilmasi",
    "error_occurred": "Xatolik yuz berdi",
    "no_pavilions": "Pavilionlar topilmadi",
    "no_blocks": "Bloklar mavjud emas",
    "no_blocks_subtitle": "Hozircha bu pavilyonda bloklar mavjud emas"
  },
  "profile": {
    "men": "Erkak",
    "women": "Ayol",
    "personal_info": "Shaxsiy ma'lumotlar",
    "personal_info_subtitle": "Profil ma'lumotlar, lavozim ma'lumotlari",
    "seller_personal_info_subtitle": "Profil ma'lumotlar, biriktirilgan joylar",
    "biometric_data": "Biometrik ma'lumotlar",
    "biometric_data_subtitle": "Tasdiqlangan yuzni ko'rish",
    "notifications": "Bildirishnomalar",
    "notifications_subtitle": "Push bildirishnomalar sozlamalari",
    "language_selection": "Tilni tanlang",
    "language_subtitle_uzbek": "Uzbek",
    "language_subtitle_russian": "Russian",
    "logout": "Chiqish",
    "logout_subtitle": "Hisobdan chiqish",
    "edit_profile": "Profilni tahrirlash",
    "save_changes": "O'zgarishlarni saqlash",
    "cancel": "Bekor qilish",
    "select_image": "Rasm tanlash",
    "camera": "Kamera",
    "gallery": "Galereya",
    "first_name": "Ism",
    "last_name": "Familiya",
    "middle_name": "Sharifi",
    "phone_number": "Telefon raqam",
    "gender": "Jinsi",
    "address": "Manzil",
    "pavilion": "Pavilion",
    "blocks": "Bloklar",
    "places": "Rastalar",
    "blocks_count": " ta blok",
    "places_count": " ta rasta",
    "default_user_name": "Foydalanuvchi",
    "loading_info": "Ma'lumot yuklanmoqda...",
    "user_data_not_found": "Foydalanuvchi ma'lumotlari topilmadi",
    "authentication_error": "Autentifikatsiya xatoligi",
    "image_selection_error": "Rasm tanlashda xatolik yuz berdi",
    "logout_confirmation": "Haqiqatan ham tizimdan chiqmoqchimisiz?",
    "logout_error": "Chiqishda xatolik yuz berdi",
    "profile_loaded_from_cache": "Profil keshdan yuklandi",
    "failed_to_load_profile": "Profilni yuklashda xatolik",
    "profile_refreshed_successfully": "Profil muvaffaqiyatli yangilandi",
    "failed_to_refresh_profile": "Profilni yangilashda xatolik",
    "failed_to_load_cached_profile": "Keshlangan profilni yuklashda xatolik",
    "profile_image_updated_successfully": "Profil rasmi muvaffaqiyatli yangilandi",
    "profile_image_updated": "Profil rasmi yangilandi",
    "profile_image_update_error": "Profil rasmini yangilashda xatolik yuz berdi",
    "old_profile_image_restored": "Eski profil rasmi qaytarildi",
    "profile_image_update_and_reload_error": "Profil rasmini yangilashda va qayta yuklashda xatolik yuz berdi",
    "old_profile_image_restored_from_cache": "Keshdan eski profil rasmi qaytarildi"
  },
  "language": {
    "dialog_title": "Tilni tanlang",
    "uzbek": "O'zbek",
    "russian": "Русский",
    "english": "English",
    "save": "Saqlash",
    "cancel": "Bekor qilish"
  },
  "home": {
    "tariff": "Tarif"
  },
  "payment": {
    "daily_rate": "Bir kunlik",
    "daily_tariff": "Kunlik tarif",
    "category": "Kategoriya",
    "tariff": "Tarif",
    "place_size": "Joy hajmi",
    "total_square": "Jami kvadrat",
    "debt": "Qarzdorlik",
    "payment_success": "To'lov muvaffaqiyatli\nqabul qilindi!",
    "payment_error": "To'lov xatoligi",
    "cash_payment_title": "uchun {amount}\nqabul qildingizmi?",
    "accept_payment": "Qabul qildim",
    "payment_creation_error": "To'lov yaratishda xatolik",
    "payment_confirmation_error": "To'lovni tasdiqlashda xatolik",
    "payment_process_error": "To'lov jarayonida xatolik"
  },
  "places": {
    "empty_place": "Bo'sh rasta",
    "unusable_place": "Yaroqsiz rasta",
    "needs_repair": "Ta'mirlash kerak",
    "dirty_place": "Iflos rasta",
    "broken_equipment": "Buzilgan jihozlar",
    "place_number": "Rasta",
    "block": "blok",
    "pavilion": "Ho'l mevalar",
    "fresh_fruits": "Ho'l mevalar",
    "no_empty_places": "Bo'sh joylar topilmadi",
    "no_empty_places_subtitle": "Hozircha ijara uchun bo'sh joylar mavjud emas",
    "category": "Kategoriya:",
    "rental_price": "Ijara narxi:",
    "contact": "Bog'lanish",
    "contact_message": "Ushbu joy haqida batafsil ma'lumot olish uchun bozor ma'muriyati bilan bog'laning.",
    "place_numbers": "Rasta raqamlari",
    "not_found": "Rastalar topilmadi",
    "not_found_subtitle": "Hozircha bu blokda rastalar mavjud emas",
    "call": "Qo'ng'iroq qilish",
    "rental_price": "Ijara: {price}"
  },
  "common": {
    "save": "Saqlash",
    "cancel": "Bekor qilish",
    "close": "Yopish",
    "ok": "OK",
    "yes": "Ha",
    "no": "Yo'q",
    "loading": "Yuklanmoqda...",
    "error": "Xatolik",
    "error_occurred": "Xatolik yuz berdi",
    "success": "Muvaffaqiyat",
    "retry": "Qayta urinish",
    "refresh": "Yangilash",
    "search": "Qidirish",
    "filter": "Filtr",
    "clear": "Tozalash",
    "no_internet_connection": "Internet aloqasi yo'q. Iltimos, ulanishni tekshiring.",
    "unknown_seller": "Noma'lum sotuvchi"
  },
  "date_picker": {
    "dialog_title": "Sanani tanlang",
    "ok": "OK",
    "cancel": "Yopish",
    "months": {
      "january": "Yanvar",
      "february": "Fevral",
      "march": "Mart",
      "april": "Aprel",
      "may": "May",
      "june": "Iyun",
      "july": "Iyul",
      "august": "Avgust",
      "september": "Sentyabr",
      "october": "Oktyabr",
      "november": "Noyabr",
      "december": "Dekabr"
    },
    "weekdays": {
      "monday": "Dushanba",
      "tuesday": "Seshanba",
      "wednesday": "Chorshanba",
      "thursday": "Payshanba",
      "friday": "Juma",
      "saturday": "Shanba",
      "sunday": "Yakshanba"
    },
    "weekdays_short": {
      "monday": "Du",
      "tuesday": "Se",
      "wednesday": "Ch",
      "thursday": "Pa",
      "friday": "Ju",
      "saturday": "Sh",
      "sunday": "Ya"
    }
  },
  "errors": {
    "network_error": "Internet aloqasi xatoligi",
    "server_error": "Server xatoligi",
    "unknown_error": "Noma'lum xatolik",
    "timeout_error": "Vaqt tugadi",
    "unauthorized": "Ruxsat berilmagan",
    "forbidden": "Taqiqlangan",
    "not_found": "Topilmadi",
    "bad_request": "Noto'g'ri so'rov",
    "internal_server_error": "Ichki server xatoligi",
    "no_internet": "Internet aloqasi yo'q",
    "payment_process_error": "To'lov jarayonida xatolik:",
    "no_data_available": "Ma'lumot mavjud emas",
    "connection_timeout": "Ulanish vaqti tugadi. Iltimos, qayta urinib ko'ring.",
    "send_timeout": "Ma'lumot yuborish vaqti tugadi. Iltimos, qayta urinib ko'ring.",
    "receive_timeout": "Ma'lumot olish vaqti tugadi. Iltimos, qayta urinib ko'ring.",
    "request_cancelled": "So'rov bekor qilindi.",
    "connection_error": "Ulanish xatoligi. Internet aloqasini tekshiring.",
    "bad_request_detailed": "Noto'g'ri so'rov. Ma'lumotlarni tekshiring.",
    "unauthorized_detailed": "Avtorizatsiya xatoligi. Qayta kiring.",
    "forbidden_detailed": "Ruxsat yo'q. Sizda bu amalni bajarish huquqi yo'q.",
    "not_found_detailed": "Ma'lumot topilmadi.",
    "conflict": "Ma'lumotlar ziddiyati.",
    "validation_error": "Ma'lumotlar formati noto'g'ri.",
    "too_many_requests": "Juda ko'p so'rov. Biroz kuting.",
    "internal_server_detailed": "Server xatoligi. Iltimos, keyinroq urinib ko'ring.",
    "bad_gateway": "Server javob bermayapti.",
    "service_unavailable": "Xizmat vaqtincha mavjud emas.",
    "server_error_with_code": "Server xatoligi ({code}).",
    "generic_error": "Kutilmagan xatolik: {error}",
    "network_check_failed": "Internet aloqasi yo'q. Iltimos, ulanishni tekshiring.",
    "network_connection_error": "Internet aloqasi yo'q. Iltimos, ulanishni tekshiring.",
    "demo_login_error": "Demo kirish xatoligi"
  },
  "image_errors": {
    "unsupported_format": "Rasm formati qo'llab-quvvatlanmaydi",
    "network_error": "Internet aloqasi yo'q",
    "corrupted_image": "Rasm buzilgan",
    "image_not_found": "Rasm topilmadi",
    "loading_error": "Rasm yuklashda xatolik"
  },
  "dialogs": {
    "empty_square": {
      "status_empty": "Bo'sh",
      "description": "Ushbu rasta hozirda bo'sh. Savdo faoliyati olib borilmayapti. Ijaraga berish uchun ochiq",
      "mark_as_empty": "Bo'sh rasta deb belgilash",
      "place_number": "Rasta #{number}"
    },
    "contact": {
      "call_button": "Qo'ng'iroq qilish"
    },
    "loading": {
      "default_message": "Yuklanmoqda...",
      "cancel": "Bekor qilish",
      "hiding_dialog": "LoadingDialog: Hiding loading dialog",
      "cannot_pop": "LoadingDialog: Cannot pop - no dialog to close"
    },
    "cash_payment": {
      "cancel": "Bekor qilish",
      "confirm_error": "To'lovni tasdiqlashda xatolik",
      "process_error": "To'lov jarayonida xatolik: {error}",
      "amount_format_comment": "Extract number from amount string like \"1 kunlik (12 000 UZS)\""
    },
    "payment_status": {
      "creating": "To'lov yaratilmoqda...",
      "existing_payment_found": "Mavjud to'lov topildi",
      "payment_created": "To'lov yaratildi",
      "confirming": "To'lov tasdiqlanmoqda...",
      "paid": "To'landi!",
      "error_occurred": "Xatolik yuz berdi",
      "pending_payment_notice": "Bu to'lov avval yaratilgan va hali tasdiqlanmagan",
      "unknown_error": "Noma'lum xatolik",
      "delete_payment": "O'chirish",
      "accept_payment": "Qabul qildim",
      "show_qr_code": "QR kodni ko'rsatish",
      "continue": "Davom etish",
      "close": "Yopish",
      "retry": "Qayta urinish",
      "cheque_number": "Chek raqami:",
      "created_at": "Yaratilgan:",
      "days_count": "Kunlar soni:",
      "status": "Holat:",
      "status_pending": "Kutilmoqda",
      "status_new": "Yangi",
      "days_unit": "kun",
      "places_label": "Joylar:",
      "delete_error": "Chekni o'chirishda xatolik",
      "delete_error_with_message": "Chekni o'chirishda xatolik: {error}",
      "creation_error": "To'lov yaratishda xatolik",
      "creation_error_with_message": "To'lov yaratishda xatolik: {error}",
      "confirm_error": "To'lovni tasdiqlashda xatolik",
      "process_error": "To'lov jarayonida xatolik: {error}",
      "unconfirmed_payment_exists": "Sizda tasdiqlanmagan to'lov mavjud.\n\nAvval mavjud to'lovni tasdiqlang yoki bekor qiling, keyin yangi to'lov yarating.",
      "success_status": "success",
      "qr_show_button": "QR kodni ko'rsatish",
      "grid_refresh_success": "Grid refresh triggered after payment success with blockId: {blockId}, pavilionId: {pavilionId}",
      "grid_refresh_error": "Error triggering grid refresh: {error}",
      "date_format_comment": "Use the date field from API response (format: \"2025-07-16 16:34\")",
      "time_empty": "",
      "date_contains_space": " ",
      "date_parts_separator": " ",
      "date_part_index_0": "2025-07-16",
      "date_part_index_1": "16:34",
      "date_format_conversion": "Format date from \"2025-07-16\" to \"16.07.2025\"",
      "date_separator": "-",
      "formatted_date_pattern": "{day}.{month}.{year}",
      "formatted_datetime": "{date} {time}",
      "cheque_id_display": "#{id}",
      "days_display": "{count} kun"
    },
    "place_check": {
      "checking": "Tekshirilmoqda...",
      "please_wait": "Iltimos, kuting",
      "success_message": "Muvaffaqiyatli tekshirildi!",
      "error_occurred": "Xatolik yuz berdi",
      "unknown_error": "Noma'lum xatolik",
      "max_retries_reached": "Maksimal urinish soni tugadi. Iltimos, keyinroq qayta urinib ko'ring.",
      "attempt_count": "Urinish: {current}/{max}",
      "cancel": "Bekor qilish",
      "close": "Yopish",
      "retry": "Qayta urinish",
      "check_error": "Tekshirishda xatolik yuz berdi",
      "unexpected_error": "Kutilmagan xatolik: {error}"
    },
    "qr_payment": {
      "title": "Click orqali to'lash",
      "place_number": "Rasta #{number}",
      "generating_qr": "QR kod yaratilmoqda...",
      "scan_instruction": "To'lovni amalga oshirish\nuchun QR-codni skanerlang",
      "check_payment": "To'lovni tekshirish",
      "retry": "Qayta urinish",
      "creation_error": "To'lov yaratishda xatolik",
      "creation_error_with_message": "To'lov yaratishda xatolik: {error}",
      "qr_generation_error": "QR kod yaratishda xatolik: {error}",
      "payment_not_found": "To'lov topilmadi",
      "check_error": "To'lov tekshirishda xatolik: {error}",
      "brightness_error": "Error setting brightness: {error}",
      "brightness_restore_error": "Error restoring brightness: {error}",
      "error_message_empty": "",
      "service_id_comment": "Replace with your actual service ID",
      "merchant_id_comment": "Replace with your actual merchant ID",
      "merchant_user_id_comment": "Replace with your actual merchant user ID"
    },
    "terminal_payment": {
      "paid_button": "To'landi",
      "cancel": "Bekor qilish",
      "confirm_error": "To'lovni tasdiqlashda xatolik",
      "process_error": "To'lov jarayonida xatolik: {error}",
      "amount_format_comment": "Extract number from amount string like \"1 kunlik (12 000 UZS)\""
    },
    "square_dialog": {
      "payment_days_question": "Necha kun uchun to'lamoqchisiz?",
      "payment_button": "To'lov qilish",
      "verified_button": "Tekshirildi",
      "mark_empty_button": "Bo'sh rasta deb belgilash",
      "select_payment_method": "To'lov usulini tanlang",
      "pay_with_click": "Click orqali to'lash",
      "pay_with_cash": "Naqd pul bilan to'lash",
      "pay_with_terminal": "Terminal orqali to'lash",
      "seller_placeholder": "-",
      "currency_uzs": "UZS",
      "square_meter": "m²",
      "debug_no_seller_payment": "Cannot proceed with payment: No valid seller",
      "debug_no_seller_cash": "Cannot proceed with cash payment: No valid seller",
      "debug_no_seller_terminal": "Cannot proceed with terminal payment: No valid seller",
      "demo_place_id": "demo_place_id",
      "debug_checking_conditions": "🔍 Checking Tekshirildi button conditions:",
      "debug_api_mode": "   - API Mode: allUnbinded={allUnbinded}, allHaveNoSeller={allHaveNoSeller}",
      "debug_squares_info": "   - Squares: {squares}",
      "debug_seller_details": "   - Seller details: {sellers}",
      "debug_final_result": "   - Final result: {result}",
      "debug_legacy_mode": "   - Legacy Mode: isUnbinded={isUnbinded}, hasNoSeller={hasNoSeller}",
      "debug_square_info": "   - Square: status={status}, seller={seller}",
      "debug_seller_found_grouped": "Found seller ID from grouped squares: {sellerId}",
      "debug_seller_null_grouped": "Seller is null or empty in grouped squares",
      "debug_seller_found_single": "Found seller ID from single square: {sellerId}",
      "debug_no_seller_found": "No seller found in either grouped squares or single square",
      "debug_demo_seller_warning": "Warning: No seller found in rasta data, using demo seller ID",
      "demo_user_id": "demo_user_id",
      "debt_amount": "{amount} UZS",
      "total_amount": "{amount} UZS"
    }
  },
  "nazoratchi": {
    "reports": {
      "empty_square_report": {
        "title": "Bo'sh rasta deb belgilash",
        "image_upload": "Rasm yuklash",
        "image_upload_subtitle": "Bo'sh joy rasmini yuklang",
        "description": "Izoh yozish",
        "submit": "Yuborish",
        "hint_text": "Ushbu rasta hozirda bo'sh. Savdo faoliyati olib borilmayapti. Ijaraga berish uchun ochiq",
        "quick_tags": {
          "empty_place": "Bo'sh rasta",
          "unusable_place": "Yaroqsiz rasta",
          "needs_repair": "Ta'mirlash kerak",
          "dirty_place": "Iflos rasta",
          "broken_equipment": "Buzilgan jihozlar"
        },
        "image_picker": {
          "select_image": "Rasm tanlash",
          "camera": "Kamera",
          "gallery": "Galereya",
          "error_selecting_image": "Rasm tanlashda xatolik yuz berdi",
          "camera_preview_message": "Iltimos, olingan rasmni tekshiring va zarur bo'lsa yo'nalishni to'g'irlang"
        },
        "success_dialog": {
          "title": "Hisobot muvaffaqiyatli yuborildi",
          "message": "Sizning hisobotingiz muvaffaqiyatli yuborildi va ko'rib chiqiladi",
          "ok_button": "Yaxshi"
        },
        "square_selection": {
          "title": "Qaysi rastani bo'shatmoqchisiz"
        }
      }
    },
    "statistics": {
      "all": "Jami",
      "plan_in_plan": "Rejada",
      "plan_received": "Tushgan",
      "plan_via_click": "Click orqali",
      "plan_cash": "Naqd pul",
      "plan_via_terminal": "Terminal orqali",
      "plan_debt": "Qarzdorlik",
      "legend_paid_places": "To'langan rastalar",
      "legend_unpaid_places": "To'lanmagan rastalar",
      "legend_unassigned_places": "Belgilanmagan rastalar",
      "legend_empty_places": "Bo'sh rastalar",
      "payment_delivered": "Topshirilgan",
      "payment_not_delivered": "Topshirilmagan"
    },
    "payment": {
      "terminal_payment_title": "{amount} uchun {formattedAmount}\nterminal orqali to'landimi?",
      "cash_payment_title": "{amount} uchun {formattedAmount}\nqabul qildingizmi?",
      "payment_rejected": "To'lov qabul qilinmadi!",
      "ok_button": "Yaxshi"
    },
    "tuzilma": {
      "no_data_available": "Ma'lumot mavjud emas",
      "error_occurred": "Xatolik yuz berdi",
      "retry": "Qayta urinish",
      "payment_impossible": "To'lov imkonsiz",
      "no_seller_info": "Ushbu rasta uchun sotuvchi ma'lumotlari mavjud emas. To'lov qilish uchun avval sotuvchi tayinlanishi kerak.",
      "default_category": "Ho'l mevalar",
      "place_number": "Rasta #{number}",
      "default_seller_name": "Azamatov Ahror",
      "default_empty_description": "Ushbu rasta hozirda bo'sh. Savdo faoliyati olib borilmayapti. Ijaraga berish uchun ochiq",
      "debt_loading_error": "Ma'lumotlarni yuklashda xatolik",
      "status": {
        "paid": "To'langan",
        "unpaid": "To'lanmagan",
        "unassigned": "Belgilanmagan",
        "empty": "Bo'sh",
        "unknown": "Noma'lum"
      },
      "errors": {
        "network_connection": "Internet aloqasi yo'q. Iltimos, ulanishni tekshiring.",
        "network_unavailable": "Internet aloqasi yo'q",
        "pavilion_load_error": "Pavilion ma'lumotlarini yuklashda xatolik yuz berdi",
        "blocks_load_error": "Bloklar ma'lumotlarini yuklashda xatolik yuz berdi",
        "unexpected_error": "Kutilmagan xatolik",
        "status_update_failed": "Status yangilanmadi",
        "report_not_sent": "Hisobot yuborilmadi",
        "image_file_not_found": "Rasm fayli topilmadi"
      },
      "success": {
        "empty_place_marked": "Bo'sh rasta muvaffaqiyatli belgilandi",
        "empty_place_mark_error": "Bo'sh rasta belgilashda xatolik yuz berdi"
      },
      "dialog": {
        "unexpected_error": "Kutilmagan xatolik yuz berdi",
        "place_number": "Rasta #{number}",
        "total_amount": "Jami summa:",
        "total_square": "Jami kvadrat:",
        "payment_days_question": "Necha kun uchun to'lamoqchisiz?",
        "category": "Kategoriya:",
        "tariff": "Tarif:",
        "daily_rate": "1 kunlik ({amount} UZS)",
        "place_size": "Joy hajmi:",
        "place_size_meters": "{size} metr",
        "debt": "Qarzdorlik",
        "last_payment_date": "Oxirgi to'lov sanasi:",
        "last_payment_amount": "Oxirgi to'lov summasi:",
        "debt_days": "{days} kun uchun",
        "seller_placeholder": "-",
        "currency_uzs": "UZS",
        "square_meter": "m²",
        "error_title": "Xatolik",
        "data_not_loaded": "Ma'lumot yuklanmadi",
        "make_payment": "To'lov qilish",
        "checked": "Tekshirildi",
        "mark_empty_place": "Bo'sh rasta deb belgilash",
        "select_payment_method": "To'lov usulini tanlang",
        "pay_with_click": "Click orqali to'lash",
        "pay_with_cash": "Naqd pul bilan to'lash",
        "pay_with_terminal": "Terminal orqali to'lash"
      }
    },
    "market_structure": {
      "blocks": "Bloklar",
      "places": "Rastalar",
      "total_places": "Jami rastalar",
      "category": "Kategoriya:",
      "tariff": "Tarif:",
      "place_size": "Joy hajmi:",
      "total_square": "Jami kvadrat:",
      "daily_rate": "1 kunlik",
      "meter": "metr",
      "debt": "Qarzdorlik",
      "total_amount": "Jami summa:",
      "legend": {
        "paid": "To'langan",
        "unpaid": "To'lanmagan",
        "empty": "Bo'sh",
        "unassigned": "Belgilanmagan"
      },
      "detailed": "Batafsil"
    }
  },
  "sotuvchi": {
    "home": {
      "title": "Asosiy",
      "tariff": "Tarif"
    },
    "payment_history": {
      "no_payments_title": "To'lov tarixi mavjud emas",
      "no_payments_subtitle": "Hozircha hech qanday to'lov amalga oshirilmagan",
      "place_number": "Rasta raqami:",
      "tariff": "Tarif:",
      "paid_date": "To'langan:"
    },
    "profile": {
      "title": "Profil",
      "logout_dialog_title": "Chiqish",
      "logout_dialog_content": "Haqiqatan ham tizimdan chiqmoqchimisiz?",
      "logout_dialog_cancel": "Bekor qilish",
      "logout_dialog_confirm": "Chiqish",
      "image_selection_error": "Rasm tanlashda xatolik yuz berdi",
      "logout_error": "Chiqishda xatolik yuz berdi",
      "load_error": "Profil ma'lumotlarini yuklashda xatolik yuz berdi",
      "image_update_error": "Profil rasmini yangilashda xatolik yuz berdi",
      "cache_loaded": "Profil keshdan yuklandi",
      "refresh_success": "Profil muvaffaqiyatli yangilandi",
      "load_failed": "Profilni yuklashda xatolik",
      "refresh_failed": "Profilni yangilashda xatolik"
    },
    "empty_places": {
      "load_error": "Bo'sh joylar ma'lumotlarini yuklashda xatolik yuz berdi",
      "network_error": "Internet aloqasi yo'q. Iltimos, ulanishni tekshiring.",
      "data_load_error": "Ma'lumotlarni yuklashda xatolik yuz berdi"
    },
    "settings": {
      "notifications": "Bildirishnomalar",
      "notifications_subtitle": "Yangi xabarlar haqida xabardor bo'ling",
      "theme": "Tema",
      "theme_subtitle": "Yorug' yoki qorong'u tema",
      "auto_refresh": "Avtomatik yangilanish",
      "auto_refresh_subtitle": "Ma'lumotlarni avtomatik yangilash",
      "theme_selection": "Mavzu",
      "theme_selection_subtitle": "Yorug' yoki qorong'u mavzu"
    }
  },
  "face_control": {
    "title": "Yuz nazorati tizimi",
    "enter_face": "Yuzni kiritish",
    "confirm_face": "Yuzni tasdiqlash",
    "settings": "Sozlamalar",
    "about": "Haqida",
    "refresh": "Yangilash",
    "please_refresh_page": "Iltimos, sahifani yangilang",
    "warning_message": "Diqqat! Yaxshi natija uchun quyidagilarga amal qiling:",
    "warning_details": "• Yoruq joyda bo'lish\n• Rasmda faqat bitta yuz bo'lishi\n• Ko'zoynak, niqob va yuzni to'sib turuvchi barcha aksessuarlardan xoli yuzni kiritish",
    "camera_preview_message": "Iltimos, olingan rasmni tekshiring, zarur bo'lsa yo'nalishni to'g'irlang. Yuzingiz tik turishi kerak!",
    "errors": {
      "check_internet": "Internet aloqasini tekshiring",
      "user_not_found": "Foydalanuvchi ma'lumotlari topilmadi",
      "unknown_role": "Noma'lum foydalanuvchi roli: ",
      "invalid_user_data": "Noto'g'ri foydalanuvchi ma'lumotlari",
      "profile_load_error": "Profilni yuklashda xatolik: ",
      "download_error": "Ma'lumotlarni yuklab bo'lmadi, FACE_IMAGE noto'g'ri!",
      "image_capture_error": "Rasmni olishda xatolik: ",
      "image_process_error": "Rasmni qayta ishlashda xatolik: ",
      "logout_error": "Chiqishda xatolik yuz berdi: "
    },
    "settings_page": {
      "title": "Sozlamalar (Nosozliklarni tuzatish uchun)",
      "camera_lens": "Kamera linzasi",
      "front": "Old",
      "thresholds": "Chegaralar",
      "liveness_level": "Jonlilik darajasi",
      "liveness_threshold": "Jonlilik chegarasi",
      "identify_threshold": "Aniqlash chegarasi",
      "reset": "Qayta o'rnatish",
      "restore_default": "Standart sozlamalarni tiklash",
      "clear_all_person": "Barcha shaxslarni o'chirish",
      "cancel": "Bekor qilish",
      "ok": "OK"
    },
    "about_page": {
      "title": "Premium Soft",
      "developer_message": "Salom! Men dasturchiman. Agar yuz nazorati bilan muammo bo'lsa, men bilan bog'laning!",
      "email": "Email: <EMAIL>",
      "phone": "Tel: +998911283725",
      "telegram": "Telegram: @flutterblogs",
      "github": "Github: https://github.com/mamasodikov"
    },
    "functional_page": {
      "unlock_app": "Ilovani ochish",
      "support_info": "Qo'llab quvatlash ma'lumotalri",
      "time_check": "Server vati bilan qurilma vatini taqqoslash",
      "location_check": "Lokatsiya aldovini tekshirish",
      "mock_location_check": "Lokatsiya aldovini tekshirish",
      "waiting": "Kuting...",
      "please_fix_issues": "Iltimos yuqoridagilarni bartaraf eting...",
      "time_difference": "Server vati bilan oradagi farq: {difference} minut"
    },
    "person_view": {
      "approved": "Tasdiqlangan",
      "not_approved": "Tasdiqlanmagan",
      "admin_only_delete": "Rasmni faqat admin o'chira oladi.."
    },
    "face_detection": {
      "face_control": "Face Control",
      "error_uploading": "Yuklashda xatolik",
      "admin_not_approved": "Admin tasdiqlamagan",
      "uploaded": "Yuklandi",
      "approved": "Tasdiqlangan",
      "photo_uploaded": "Rasm yuklandi",
      "similarity": "O'xshashlik: {percent}%",
      "liveness": "Jonlilik: {percent}%",
      "liveness_calculating": "O'xshashlik..",
      "upload_error_message": "Xatolik: {message}"
    }
  },
  "camera": {
    "title": "Rasm olish",
    "selected_image": "Tanlangan rasm",
    "captured_image": "Olingan rasm",
    "camera_preparing": "Kamera tayyorlanmoqda...",
    "gallery": "Galereya",
    "settings": "Sozlamalar",
    "camera_settings": "Kamera sozlamalari",
    "hd_quality": "Yuqori sifat (HD)",
    "hd_quality_subtitle": "Rasm sifatini yaxshilaydi",
    "grid_lines": "Yo'riqnoma chiziqlari",
    "grid_lines_subtitle": "Rasm kompozitsiyasi uchun",
    "auto_save": "Avtomatik saqlash",
    "auto_save_subtitle": "Rasmlarni galereyaga avtomatik saqlash",
    "image_format": "Rasm formati",
    "image_format_current": "Hozirgi: {format}",
    "select_image_format": "Rasm formatini tanlang",
    "jpeg_description": "Kichik fayl hajmi, yaxshi sifat",
    "png_description": "Katta fayl hajmi, eng yaxshi sifat",
    "cancel": "Bekor qilish",
    "image_saved_to_gallery": "Rasm galereyaga saqlandi",
    "save_error": "Saqlashda xatolik: {error}",
    "gallery_selection_error": "Galereyadan rasm tanlashda xatolik: {error}",
    "camera_error": "Error camera_page: {error}",
    "rotation_error": "Rotated result doesn't exist!"
  },
  "location": {
    "turn_on_location": "Lokatsiyani yoqing",
    "give_location_permission": "Lokatsiyaga ruxsat bering",
    "fraud_location": "Lokatsiyada aldov aniqlandi",
    "turn_on_location_message": "Iltimos, ilova to'g'ri ishlashi uchun lokatsiyani yoqing!",
    "give_location_permission_message": "Lokatsiyaga ruxsat bering",
    "fraud_location_message": "Lokatsiyada aldov aniqlandi!"
  },
  "statistics_history": {
    "error_occurred": "Xatolik yuz berdi",
    "load_more_error": "Qo'shimcha ma'lumotlarni yuklashda xatolik",
    "data_load_error": "Ma'lumotlarni yuklashda xatolik yuz berdi",
    "retry": "Qayta urinish",
    "no_data_found": "Ma'lumotlar topilmadi",
    "places_header": "Rastalar",
    "amount_header": "Summa (UZS)",
    "payment_history_load_error": "To'lov tarixini yuklashda xatolik yuz berdi",
    "statistics_load_error": "Statistikasini yuklashda xatolik yuz berdi",
    "invalid_response_format": "Noto'g'ri javob formati"
  },
  "camera": {
    "settings_title": "Kamera sozlamalari",
    "hd_quality": "Yuqori sifat (HD)",
    "hd_quality_subtitle": "Rasm sifatini yaxshilaydi",
    "grid_lines": "Yo'riqnoma chiziqlari",
    "grid_lines_subtitle": "Rasm kompozitsiyasi uchun",
    "auto_save": "Avtomatik saqlash",
    "auto_save_subtitle": "Rasmlarni galereyaga avtomatik saqlash",
    "image_format": "Rasm formati",
    "image_format_current": "Hozirgi: {format}",
    "select_image_format": "Rasm formatini tanlang",
    "jpeg_description": "Kichik fayl hajmi, yaxshi sifat",
    "png_description": "Katta fayl hajmi, eng yaxshi sifat",
    "cancel": "Bekor qilish",
    "gallery_selection_error": "Galereyadan rasm tanlashda xatolik: {error}",
    "image_saved_to_gallery": "Rasm galereyaga saqlandi",
    "save_error": "Saqlashda xatolik: {error}",
    "capture_error": "Rasm olishda xatolik: {error}"
  },
  "face_control": {
    "errors": {
      "user_not_found": "Foydalanuvchi topilmadi",
      "unknown_role": "Noma'lum rol: ",
      "invalid_user_data": "Noto'g'ri foydalanuvchi ma'lumotlari",
      "profile_load_error": "Profil yuklashda xatolik: ",
      "download_error": "Yuklab olishda xatolik",
      "image_capture_error": "Rasm olishda xatolik: ",
      "image_process_error": "Rasmni qayta ishlashda xatolik: ",
      "logout_error": "Chiqishda xatolik: ",
      "face_not_detected": "Yuz aniqlanmadi"
    }
  },
  "payment": {
    "errors": {
      "bad_request": "Noto'g'ri ma'lumotlar yuborildi",
      "unauthorized": "Avtorizatsiya xatoligi",
      "not_found": "To'lov topilmadi",
      "conflict": "To'lov allaqachon mavjud",
      "server_error": "Server xatoligi",
      "payment_error": "To'lov jarayonida xatolik yuz berdi",
      "unexpected_error": "Kutilmagan xatolik yuz berdi"
    },
    "types": {
      "cash": "Naqd pul bilan to'lash",
      "terminal": "Terminal orqali to'lash",
      "qr": "Click orqali to'lash",
      "cash_short": "Naqd",
      "terminal_short": "Terminal",
      "qr_short": "Click"
    }
  },
  "market_structure": {
    "no_pavilions_subtitle": "Hozircha pavilionlar mavjud emas"
  }
};
static const Map<String, Map<String,dynamic>> mapLocales = {"ru": _ru, "en": _en, "uz": _uz};
}
